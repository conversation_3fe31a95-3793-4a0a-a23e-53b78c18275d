version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: astrobakend_mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: astrobakend
      MYSQL_USER: astrouser
      MYSQL_PASSWORD: astropass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password

volumes:
  mysql_data:
