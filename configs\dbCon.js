const { Sequelize } = require("sequelize");
require("dotenv").config();

const Sequelizer = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    dialect: process.env.DB_DIALECT,
    port: process.env.DB_PORT || 3306,
    logging: false,
    pool: { max: 10, min: 0, acquire: 30000, idle: 10000 },
  }
);

const connectDB = async () => {
  try {
    console.log("🔄 Attempting to connect to database...");
    console.log(`📍 Host: ${process.env.DB_HOST}:${process.env.DB_PORT}`);
    console.log(`🗄️  Database: ${process.env.DB_NAME}`);
    console.log(`👤 User: ${process.env.DB_USER}`);

    await Sequelizer.authenticate();
    console.log("DB Connected ✅ To MariaDB/MySQL Via Mysql2");

    if(process.env.SERVER_TYPE === "PRODUCTION"){
      await Sequelizer.sync({ alter: false });
      console.log("DB Synced ✅ (Production Mode - No Alterations)");
    }else if ( process.env.SERVER_TYPE === "STAGING" ){
      // await Sequelizer.sync({ force: true });
      // console.log("DB Synced ✅");
    }
    await Sequelizer.sync({ alter: true });
    console.log("DB Synced ✅ (Development Mode - Auto Alter)");
  } catch (error) {
    console.error("DB Connection ❌ Failed !...");
    console.error("Error Details:", error.message);
    console.error("Error Code:", error.code);

    // Provide specific error guidance
    if (error.code === 'ECONNREFUSED') {
      console.error("💡 Solution: MySQL/MariaDB server is not running!");
      console.error("   - Install MySQL/MariaDB, or");
      console.error("   - Run: docker-compose up -d (if using Docker)");
      console.error("   - Or start your local MySQL service");
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error("💡 Solution: Check your database credentials in .env file");
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error("💡 Solution: Database 'astrobakend' doesn't exist. Create it first!");
    }

    throw error; // Re-throw to let the caller handle it
  }
};

module.exports = { connectDB, Sequelizer };
